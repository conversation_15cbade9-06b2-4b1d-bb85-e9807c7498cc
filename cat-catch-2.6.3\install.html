<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <title>猫抓 cat-catch</title>
    <link rel="stylesheet" type="text/css" href="css/public.css" media="all" />
    <link rel="stylesheet" type="text/css" href="css/options.css" media="all" />
    <script src="js/function.js"></script>
</head>

<body>
    <div class="wrapper1024">
        <section>
            <div class="optionBox">
                <p style="font-size: xx-large;text-align: center;margin-bottom: 0.5rem;color: var(--link-color);">恭喜 猫抓
                    扩展已成功安装 !</p>
                <p style="font-size: xx-large;text-align: center;margin-bottom: 2rem;color: var(--link-color);">
                    Installation successful !</p>
                <p>希望本扩展能帮助到你。请仔细阅读以下协议和免责声明，使用过程中出现问题，请到 <a
                        href="https://cat-catch.bmmmd.com/issues">https://cat-catch.bmmmd.com/issues</a> 提交问题</p>
                <br>
                <p>I hope this extension can help you.</p>
                <p>Please read the following agreement and disclaimer carefully. If you encounter any issues during use,
                    please submit them to <a href="https://github.com/xifangczy/cat-catch/issues">GitHub Issues</a></p>
            </div>
        </section>

        <section>
            <h1 class="optionsTitle" style="text-align: center;">隐私政策 / Privacy Policy</h1>
            <div class="optionBox">
                <p>本扩展收集所有信息都在本地储存处理，不会发送到远程服务器，不包含任何跟踪器。</p>
                <br>
                <p>The extension collects and processes all information locally without sending it to remote servers and
                    does not include any trackers.</p>
            </div>
        </section>

        <section>
            <h1 class="optionsTitle" style="text-align: center;">免责声明 / Disclaimer</h1>
            <div class="optionBox">
                <p>本扩展仅供下载用户拥有版权或已获授权的视频，禁止用于下载受版权保护且未经授权的内容。用户需自行承担使用本工具的全部法律责任，开发者不对用户的任何行为负责。本工具按“原样”提供，开发者不承担任何直接或间接责任。
                </p>
                <br>
                <p>This extension is intended for downloading videos that you own or have authorized access to. It is
                    prohibited to use this Tool for downloading copyrighted content without permission. Users are solely
                    responsible for their actions, and the developer is not liable for any user behavior. This Tool is
                    provided "as-is," and the developer assumes no direct or indirect liability.</p>
            </div>
        </section>

        <section>
            <h1 class="optionsTitle" style="text-align: center;"></h1>
            <div class="optionBox" style="text-align: center;">
                <p>点击“同意”或“关闭本页面”即表示您已阅读并同意以上内容。</p>
                <p>By clicking "Agree" or "Close this page," you confirm that you have read and agree to the above
                    terms.</p>
                <button id="installYes" class="button2">同意 / Agree</button>
                <button id="installUninstallSelf" class="openDir">卸载扩展 / Uninstall Self</button>
            </div>
        </section>
    </div>
    <script src="js/install.js"></script>
</body>

</html>