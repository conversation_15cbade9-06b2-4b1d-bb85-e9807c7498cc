<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>catCatch</title>
  <link rel="stylesheet" type="text/css" href="css/public.css" media="all" />
  <link rel="stylesheet" type="text/css" href="css/popup.css" media="all" />
  <script src="js/init.js"></script>
  <script src="lib/jquery.min.js"></script>
  <script src="js/function.js"></script>
</head>

<body class="popupBody">
  <div class="Tabs">
    <div id="currentTab" class="TabButton flex Active">
      <span data-i18n-outer="currentPage"></span>
      <div id="quantity"></div>
    </div>
    <div id="allTab" class="TabButton flex">
      <span data-i18n-outer="otherPage"></span>
      <div id="quantity"></div>
    </div>
    <div id="otherTab" class="TabButton">
      <span data-i18n-outer="otherFeatures"></span> / <span data-i18n-outer="mediaControl"></span>
    </div>
    <div id="maybeKeyTab" class="TabButton hide">
      <span data-i18n-outer="possibleKey"></span>
    </div>
  </div>
  <div id="Tips" data-i18n="loadingData">
  </div>
  <div id="TipsFixed">~</div>
  <div id="mediaList" class="container hide TabShow"></div>
  <div id="allMediaList" class="container hide"></div>
  <div id="otherOptions" class="container hide">
    <div class="otherTips" data-i18n="mediaControl"></div>
    <div class="flexRow">
      <b class="nowrap" data-i18n="selectWebpage"></b>
      <select id="videoTabIndex">
        <option value="-1" id="videoTabTips" data-i18n="noMediaDetected"></option>
      </select>
    </div>
    <div class="flexRow">
      <b class="nowrap" data-i18n="selectMedia"></b>
      <select id="videoIndex">
        <option value="-1" id="videoIndexTips" data-i18n="noControllableMediaDetected"></option>
      </select>
    </div>
    <div id="PlayControl" class="textColor">
      <span data-i18n-outer="multiplier"></span><input id="playbackRate" type="number" value="2" min="0.1" max="16"
        step="1" />
      <button id="speed" class="button2" data-i18n="speedPlayback"></button>
      <button id="control" data-switch="play" data-i18n="play"></button>
      <button id="pip" class="firefoxHide" data-i18n="pictureInPicture"></button>
      <button id="fullScreen" class="firefoxHide" data-i18n="fullscreen"></button>
      <button id="screenshot" data-switch="play" data-i18n="screenshot"></button>
      <label class="flexColumn loop">
        <div data-i18n="loop"></div><input type="checkbox" id="loop">
      </label>
      <label class="flexColumn muted">
        <div data-i18n="mute"></div><input type="checkbox" id="muted">
      </label>
      <div class="flexColumn">
        <div data-i18n="volume"></div><input type="range" id="volume" class="volume" min="0" max="1" value="1"
          step="0.01" />
      </div>
      <div class="flexColumn width100">
        <div id="timeShow"></div><input type="range" id="time" class="width100" min="0" max="100" value="0" step="1" />
      </div>
    </div>
    <div class="line"></div>
    <div class="otherTips" data-i18n="functionEntry"></div>
    <div class="otherFeat flexRow">
      <button go="downloader.html" class="button2" data-i18n="downloader"></button>
      <button go="m3u8.html" class="button2" data-i18n="m3u8Parser"></button>
      <button go="mpd.html" class="button2" data-i18n="mpdParser"></button>
      <button go="json.html" class="button2" data-i18n="jsonFormatter"></button>
      <button go="ffmpegURL" class="button2">FFmpeg</button>
    </div>
  </div>
  <div id="maybeKey" class="container hide"></div>
  <div id="filter" class="more">
    <span id="regular" class="width100 regular">
      <input type="text" data-i18n-placeholder="regularFilterPlaceholder" /><button id="regularFilter" class="hide"
        data-i18n="confirm"></button>
    </span>
    <div id="ext"></div>
  </div>
  <div id="features" class="more flex-end">
    <button id="unfoldAll" data-i18n="expandAll"></button>
    <button id="unfoldPlay" data-i18n="expandPlayable"></button>
    <button id="unfoldFilter" data-i18n="expandSelected"></button>
    <button id="fold" data-i18n="collapseAll"></button>
    <button id="recorder" class="button2 hide firefoxHideScript" type="script" data-i18n="videoRecording"></button>
    <button id="webrtc" class="button2 hide firefoxHideScript" type="script" data-i18n="recordWebRTC"></button>
    <button id="recorder2" class="button2 hide" type="script" data-i18n="screenCapture"></button>
    <button id="MobileUserAgent" class="button2 firefoxHideScript" data-i18n="simulateMobile"></button>
    <button id="AutoDown" class="button2" data-i18n="autoDownload"></button>
    <button id="currentPage" class="hide" data-i18n="currentPage"></button>
    <button id="send2localSelect" class="button2" data-i18n="send2local"></button>
  </div>
  <div id="down">
    <button id="mergeDown" class="button2" data-i18n="onlineMerge" disabled></button>
    <button id="DownFile" data-i18n="download"></button>
    <button id="AllCopy" data-i18n="copy"></button>
    <button id="AllSelect" data-i18n="selectAll"></button>
    <button id="invertSelection" data-i18n="invertSelection"></button>
    <button id="openFilter" panel="filter" data-i18n="filter"></button>
    <button id="Clear" data-i18n="clear"></button>
    <button id="search" class="button2 hide firefoxHideScript" type="script" data-i18n="deepSearch"></button>
    <button id="catch" class="button2 hide firefoxHideScript" type="script" data-i18n="cacheCapture"></button>
    <button id="more" class="button2" panel="features" data-i18n="moreFeatures"></button>
    <button id="enable" class="button2" data-i18n="pause"></button>
    <button id="Options" go="options.html" class="button2" data-i18n="settings"></button>
    <button id="popup" class="button2" data-i18n="popup"></button>
  </div>
  <!-- <script src="js/firefox.js"></script> -->
  <script src="lib/base64.js"></script>
  <script src="js/pupup-utils.js"></script>
  <script src="js/popup.js"></script>
  <script src="js/media-control.js"></script>
  <script src="lib/jquery.qrcode.min.js"></script>
  <script src="lib/hls.min.js"></script>
  <script src="js/i18n.js"></script>
</body>

</html>