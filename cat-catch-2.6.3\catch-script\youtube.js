// YouTube 视频嗅探脚本
// 专门用于检测和捕获 YouTube 视频资源

(function() {
    'use strict';
    
    // 调试模式
    const YOUTUBE_DEBUG = false;
    
    // 已处理的视频集合，避免重复处理
    const processedVideos = new Set();
    
    // YouTube 视频 URL 模式
    const youtubePatterns = {
        videoplayback: /https:\/\/.*\.googlevideo\.com\/videoplayback\?.*$/i,
        timedtext: /https:\/\/.*\.youtube\.com\/api\/timedtext\?.*$/i,
        manifest: /https:\/\/.*\.youtube\.com\/api\/manifest\/dash\/.*$/i,
        thumbnail: /https:\/\/.*\.ytimg\.com\/vi\/.*$/i,
        playerResponse: /https:\/\/.*\.youtube\.com\/youtubei\/v1\/player\?.*$/i,
        webPlayerConfig: /https:\/\/.*\.youtube\.com\/s\/player\/.*$/i,
        audioplayback: /https:\/\/.*\.googlevideo\.com\/videoplayback\?.*mime=audio.*$/i
    };
    
    // 发送数据到扩展
    function postData(data) {
        try {
            window.postMessage({
                action: "catCatchAddMedia",
                ...data
            }, "*");
        } catch (e) {
            YOUTUBE_DEBUG && console.error("YouTube Sniffer: Failed to post data", e);
        }
    }
    
    // 获取视频标题
    function getVideoTitle() {
        const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string') ||
                           document.querySelector('h1.title') ||
                           document.querySelector('meta[property="og:title"]');
        
        if (titleElement) {
            return titleElement.textContent || titleElement.getAttribute('content') || 'YouTube Video';
        }
        return 'YouTube Video';
    }
    
    // 获取视频 ID
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v') || 'unknown';
    }
    
    // 检查 URL 是否为 YouTube 视频资源
    function isYouTubeVideoResource(url) {
        return Object.values(youtubePatterns).some(pattern => pattern.test(url));
    }
    
    // 处理视频 URL
    function processVideoUrl(url, type = 'mp4') {
        if (processedVideos.has(url)) {
            return;
        }
        
        processedVideos.add(url);
        
        const videoTitle = getVideoTitle();
        const videoId = getVideoId();
        
        YOUTUBE_DEBUG && console.log("YouTube Sniffer: Found video resource", {
            url: url,
            type: type,
            title: videoTitle,
            videoId: videoId
        });
        
        postData({
            url: url,
            href: location.href,
            ext: type,
            title: videoTitle,
            videoId: videoId,
            source: 'youtube-sniffer'
        });
    }
    
    // 解析 YouTube 播放器响应
    function parsePlayerResponse(responseText) {
        try {
            const data = JSON.parse(responseText);
            if (data.streamingData) {
                // 处理自适应格式
                if (data.streamingData.adaptiveFormats) {
                    data.streamingData.adaptiveFormats.forEach(format => {
                        if (format.url) {
                            const ext = format.mimeType ? format.mimeType.split('/')[1].split(';')[0] : 'mp4';
                            processVideoUrl(format.url, ext);
                        }
                    });
                }

                // 处理常规格式
                if (data.streamingData.formats) {
                    data.streamingData.formats.forEach(format => {
                        if (format.url) {
                            const ext = format.mimeType ? format.mimeType.split('/')[1].split(';')[0] : 'mp4';
                            processVideoUrl(format.url, ext);
                        }
                    });
                }
            }
        } catch (e) {
            YOUTUBE_DEBUG && console.error("YouTube Sniffer: Failed to parse player response", e);
        }
    }

    // 拦截 XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this.addEventListener('readystatechange', function() {
            if (this.readyState === 4 && this.status === 200) {
                if (isYouTubeVideoResource(url)) {
                    let ext = 'mp4';
                    if (youtubePatterns.timedtext.test(url)) {
                        ext = 'xml';
                    } else if (youtubePatterns.manifest.test(url)) {
                        ext = 'mpd';
                    } else if (youtubePatterns.thumbnail.test(url)) {
                        ext = 'jpg';
                    } else if (youtubePatterns.audioplayback.test(url)) {
                        ext = 'm4a';
                    }

                    processVideoUrl(url, ext);
                }

                // 检查是否为播放器响应
                if (youtubePatterns.playerResponse.test(url) && this.responseText) {
                    parsePlayerResponse(this.responseText);
                }
            }
        });

        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    // 拦截 fetch
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;

        const promise = originalFetch.apply(this, arguments);

        promise.then(response => {
            if (response.ok && isYouTubeVideoResource(url)) {
                let ext = 'mp4';
                if (youtubePatterns.timedtext.test(url)) {
                    ext = 'xml';
                } else if (youtubePatterns.manifest.test(url)) {
                    ext = 'mpd';
                } else if (youtubePatterns.thumbnail.test(url)) {
                    ext = 'jpg';
                } else if (youtubePatterns.audioplayback.test(url)) {
                    ext = 'm4a';
                }

                processVideoUrl(url, ext);
            }

            // 检查播放器响应
            if (response.ok && youtubePatterns.playerResponse.test(url)) {
                response.clone().text().then(text => {
                    parsePlayerResponse(text);
                }).catch(e => {
                    YOUTUBE_DEBUG && console.error("YouTube Sniffer: Failed to read response text", e);
                });
            }
        }).catch(error => {
            YOUTUBE_DEBUG && console.error("YouTube Sniffer: Fetch error", error);
        });

        return promise;
    };
    
    // 监听页面中的视频元素
    function observeVideoElements() {
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            if (video.src && !processedVideos.has(video.src)) {
                YOUTUBE_DEBUG && console.log("YouTube Sniffer: Found video element", video.src);
                processVideoUrl(video.src);
            }
        });
    }
    
    // 监听 DOM 变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查新添加的视频元素
                        if (node.tagName === 'VIDEO') {
                            if (node.src && !processedVideos.has(node.src)) {
                                processVideoUrl(node.src);
                            }
                        }
                        
                        // 检查子元素中的视频
                        const videos = node.querySelectorAll && node.querySelectorAll('video');
                        if (videos) {
                            videos.forEach(video => {
                                if (video.src && !processedVideos.has(video.src)) {
                                    processVideoUrl(video.src);
                                }
                            });
                        }
                    }
                });
            }
        });
    });
    
    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // 初始检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', observeVideoElements);
    } else {
        observeVideoElements();
    }
    
    // 定期检查（作为备用方案）
    setInterval(observeVideoElements, 5000);
    
    YOUTUBE_DEBUG && console.log("YouTube Sniffer: Script loaded and active");
    
})();
