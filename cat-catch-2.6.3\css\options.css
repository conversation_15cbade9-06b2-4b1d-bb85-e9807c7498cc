body {
  background: var(--background-color);
  font-size: 13px;
  font-family: "Microsoft YaHei", "Helvetica", "Arial", sans-serif;
  margin: 0;
}

.wrapper {
  margin: 0 auto;
  width: 45rem;
}

.error {
  color: var(--text-error-color);
}

h1 {
  font-size: 1.125em;
  font-weight: normal;
  margin: 0;
}

h2 {
  font-size: 1.125em;
  font-weight: normal;
  margin: 0;
}

p {
  margin: auto;
}

.optionBox {
  background: var(--optionBox-color);
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgb(60 64 67 / 30%), 0 1px 3px 1px rgb(60 64 67 / 15%);
  padding: 0.75em 1.25em;
  margin-top: 5px;
}

table {
  width: 100%;
  text-align: center;
}

input,
textarea {
  padding: 5px 5px;
}

input.ext {
  width: 100px;
  text-align: center;
}

input.type {
  width: 200px;
  text-align: center;
}

input.size {
  width: 100px;
  text-align: center;
}

input.regexType {
  width: 20px;
  text-align: center;
}

input.regexExt {
  width: 35px;
  text-align: center;
}

input.regex {
  width: 320px;
  text-align: center;
}

/* input#OtherAutoClear {
  margin-left: 250px;
  width: 45px;
} */
/* 滑动开关 组件 */
.switch {
  height: 22px;
  width: 50px;
  margin: auto;
}

.switch .switchRound {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  background-color: var(--switch-off-color);
  transition: all 0.2s ease-in-out;
}

.switch .switchRoundBtn {
  display: block;
  position: absolute;
  top: 2px;
  left: 3px;
  bottom: 3px;
  width: 18px;
  height: 18px;
  background-color: var(--switch-round-color);
  transition: all 0.2s ease-in-out;
}

.switch .switchInput {
  display: none;
}

.switch .switchInput:checked + .switchRound {
  background-color: var(--switch-on-color);
}

.switch .switchInput:checked + .switchRound > .switchRoundBtn {
  left: 29px;
}

.switch .switchRadius {
  border-radius: 50px;
}

/* 滑动开关 组件 END */
.list {
  padding-left: 10px;
  padding-top: 5px;
}

.item {
  align-items: center;
  display: flex;
  min-height: 30px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.06);
  flex-wrap: wrap;
  align-items: flex-end;
  align-content: space-around;
}

.item .switch {
  margin-right: 50px;
}

.item .switchSelect {
  margin-right: 85px;
}

.optionsTitle {
  margin-top: 20px;
}

.RemoveButton {
  fill: var(--text-color);
  height: 20px;
  cursor: pointer;
}

button,
.button,
.button2 {
  padding: calc(0.5em - 1px) 1em;
  margin: 5px 5px 5px 5px;
  /* font-size: 13px; */
}

.flex-end {
  display: flex;
  justify-content: flex-end;
}

.explain {
  color: #6c6c6c;
}

#typeList,
#extList {
  margin-top: 10px;
}

.otherOption .item {
  margin-bottom: 5px;
  min-height: 35px;
}

#m3u8_url,
#mpd_url,
.test_url {
  overflow: hidden;
  display: block;
  text-overflow: ellipsis;
  word-break: break-all;
  color: var(--text2-color);
}

.block {
  border-bottom: solid 1px rgba(0, 0, 0, 0.06);
  padding-bottom: 5px;
  margin-bottom: 5px;
}

.m3u8_wrapper .block {
  border-bottom: 0px;
}

.wrapper1024 {
  margin: 0 auto;
  width: 1024px;
}

.wrapper1080 {
  margin: 0 auto;
  width: 1080px;
}

textarea {
  font-size: 12px;
  font-family: "Microsoft YaHei", "Helvetica", "Arial", sans-serif;
}

#textarea {
  text-align: center;
}

.m3u8_wrapper video {
  max-height: 80vh;
  max-width: 100%;
}

#media_file {
  word-break: break-all;
}

#media_file,
#jsonText,
#m3u8Text {
  height: 55vh;
}

/* #media_file {
  font-size: 12px;
  font-family: "Microsoft YaHei", "Helvetica", "Arial", sans-serif;
  height: 700px;
  overflow-y: auto;
  border: solid 1.5px rgb(0 0 0 / 50%);
  word-break: break-all;
} */
#formatStr {
  width: 145px;
}

#tips input {
  color: var(--text2-color);
}

.keyUrl {
  width: 1034px;
}

.fullInput {
  /* width: 975px; */
  width: 100%;
  margin: 5px 0 5px 0;
}

.select {
  appearance: none;
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIxMiIgZmlsbD0iIzVGNjM2OCI+PHBhdGggZD0iTTAgMGgyNEwxMiAxMnoiLz48L3N2Zz4=)
    calc(100% - 8px) center no-repeat;
  /* background-color: rgb(241, 243, 244); */
  background-color: var(--background-color-two);
  background-size: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 5px 10px;
}

.select {
  width: 8rem;
}

.m3u8Key {
  width: 300px;
}

#PlayerTemplate {
  width: 200px;
}

#errorTsList p {
  color: red;
  word-break: break-all;
}

.progress-bar {
  height: 15px;
  background-color: rgb(189, 193, 198);
  border-radius: 3px;
  margin: 3px;
  margin-bottom: 10px;
}

.progress {
  width: 0px;
  height: 100%;
  background-color: var(--text2-color);
  border-radius: 3px;
}

#fileSize,
#fileDuration {
  margin-left: 20px;
}

.not-allowed {
  cursor: not-allowed;
  background-color: #ccc;
  color: #fff;
}

.not-allowed:hover {
  background: #ccc;
}

.not-allowed:active {
  background: #ccc;
}

#showM3u8Help {
  margin-left: 10px;
  margin-top: 1px;
  margin-right: 0px;
  padding: 2px;
}

.m3u8checkbox {
  display: flex;
  cursor: pointer;
  flex-direction: column;
  user-select: none;
  margin: 0 5px 0 5px;
}

.merge {
  display: flex;
  justify-content: flex-start;
  margin-top: 5px;
  align-items: center;
}

.customKey input {
  margin-right: 5px;
}

/* .wrapper .button {
  margin-top: 5px;
} */
.rangeDown {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 10px;
}

.rangeDown .merge {
  margin-top: 0;
}

#rangeStart,
#rangeEnd {
  width: 55px;
  /* text-align:center;
  vertical-align:middle; */
  margin-left: 2px;
  margin-right: 2px;
  padding-top: 3px;
  padding-bottom: 3px;
}

#loading a {
  word-break: break-all;
}

#next_m3u8 a {
  word-break: break-all;
}

.key {
  align-items: flex-end;
}

.key div {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

.key input {
  width: 265px;
}

.method input {
  width: 100px;
}

.offset {
  width: 256px;
}

.videoInfo div {
  margin-right: 5px;
}

.flex {
  display: flex;
}

.m3u8dlArg {
  margin-top: 10px;
  height: 100px;
  word-break: break-all;
  width: 100%;
}

.m3u8DL {
  margin-right: 70px !important;
}

/* .m3u8DL #m3u8dl{
  width: 8rem;
} */
.break-all {
  word-break: break-all;
}

/* MPD*/
.dash .select {
  padding-right: 20px;
  margin-bottom: 10px;
}

/* JSON格式化 */
.json-document {
  margin-top: 0px;
}

ul.json-dict,
ol.json-array {
  list-style-type: none;
  margin: 0 0 0 1px;
  border-left: 1px dotted #ccc;
  padding-left: 2em;
}

.json-string {
  color: #0b7500;
  word-break: break-all;
  white-space: break-spaces;
}

.json-literal {
  color: #1a01cc;
  font-weight: bold;
}

a.json-toggle {
  position: relative;
  color: inherit;
  text-decoration: none;
}

a.json-toggle:focus {
  outline: none;
}

a.json-toggle:before {
  font-size: 1.1em;
  color: #c0c0c0;
  content: "\25BC";
  position: absolute;
  display: inline-block;
  width: 1em;
  text-align: center;
  line-height: 1em;
  left: -1.2em;
}

a.json-toggle:hover:before {
  color: #aaa;
}

a.json-toggle.collapsed:before {
  transform: rotate(-90deg);
}

a.json-placeholder {
  color: #aaa;
  padding: 0 1em;
  text-decoration: none;
}

a.json-placeholder:hover {
  text-decoration: underline;
}

#downList a {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  color: var(--text2-color);
}

#downList {
  overflow: scroll;
  height: 60vh;
  text-align: left;
  display: none;
  width: 100%;
  border: solid 1px var(--text-color);
}

.width3rem {
  width: 3rem;
}

.popupAttr {
  margin-left: 0.5rem;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-wrapper {
  flex: 1;
}

.newDownload .downItem {
  margin-bottom: 1rem;
}

.newDownload .downItem .progress-bar {
  margin-bottom: 0;
  height: 20px;
}

.newDownload .downItem button {
  margin: 0;
}

.newDownload .downItem .progress {
  color: var(--background-color-two);
  text-align: center;
  transition: width 0.2s;
}

/** 导航条 **/
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 10rem;
  height: 100%;
  padding: 10px;
  background-color: var(--background-color-two);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  text-align: center;
  margin-right: 0;
}
.sidebar ul {
  list-style-type: none;
  padding: 0;
}
.sidebar li {
  margin: 10px 0;
}
.sidebar a {
  text-decoration: none;
  color: var(--text-color);
  display: block;
  padding: 5px;
  border-radius: 4px;
}
.sidebar a:hover {
  background-color: var(--button-hover-color);
}

.item .send2localType {
  margin-right: 196px;
}
.item .send2localType select {
  width: 15rem;
}
