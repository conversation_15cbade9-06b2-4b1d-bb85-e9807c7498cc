# YouTube 嗅探功能说明

## 功能概述

Cat Catch 现在支持 YouTube 视频嗅探功能，可以自动检测和捕获 YouTube 视频资源，包括：

- 视频流 (MP4, WebM 等格式)
- 音频流 (M4A, AAC 等格式)
- DASH 清单文件 (MPD)
- 字幕文件 (XML/SRT)
- 缩略图 (JPG)

## 启用方法

1. **安装扩展**：确保 Cat Catch 扩展已正确安装并启用
2. **启用 YouTube 脚本**：
   - 打开 Cat Catch 扩展弹出窗口
   - 找到 "YouTube 嗅探" 选项
   - 点击启用该脚本
3. **访问 YouTube**：打开 YouTube 网站并播放视频
4. **查看捕获结果**：在 Cat Catch 扩展中查看捕获到的视频资源

## 技术实现

### 1. 正则匹配规则

扩展会自动匹配以下 URL 模式：

```javascript
// 视频播放 URL
https://*.googlevideo.com/videoplayback?*

// 字幕文件 URL  
https://*.youtube.com/api/timedtext?*

// DASH 清单 URL
https://*.youtube.com/api/manifest/dash/*

// 音频流 URL (包含 mime=audio 参数)
https://*.googlevideo.com/videoplayback?*mime=audio*

// 缩略图 URL (默认禁用)
https://*.ytimg.com/vi/*/*.jpg
```

### 2. 脚本注入

`youtube.js` 脚本会被注入到 YouTube 页面中，执行以下操作：

- 拦截 XMLHttpRequest 和 fetch 请求
- 解析 YouTube 播放器响应数据
- 监听页面中的视频元素变化
- 自动提取视频标题和 ID 信息

### 3. 数据处理

脚本会自动：
- 识别不同类型的媒体资源
- 避免重复处理相同的 URL
- 提取视频元数据（标题、ID 等）
- 发送数据到扩展后台进行处理

## 支持的资源类型

| 资源类型 | 文件扩展名 | 说明 |
|---------|-----------|------|
| 视频流 | mp4, webm | 主要的视频文件 |
| 音频流 | m4a, aac | 音频轨道 |
| 清单文件 | mpd | DASH 自适应流清单 |
| 字幕文件 | xml, srt | 视频字幕 |
| 缩略图 | jpg | 视频预览图 |

## 使用注意事项

1. **合法使用**：请确保您的使用符合 YouTube 的服务条款和当地法律法规
2. **性能影响**：脚本会监听网络请求，可能对页面性能有轻微影响
3. **兼容性**：支持现代浏览器，建议使用 Chrome 102+ 或 Firefox 113+
4. **调试模式**：可以在脚本中启用 `YOUTUBE_DEBUG` 来查看详细日志

## 测试功能

使用 `youtube-test.html` 文件来测试嗅探功能：

1. 在浏览器中打开 `youtube-test.html`
2. 启用 Cat Catch 的 YouTube 嗅探脚本
3. 点击测试按钮模拟各种 YouTube 请求
4. 检查扩展是否正确捕获了模拟的资源

## 故障排除

### 问题：脚本未启用
- 解决：在 Cat Catch 弹出窗口中手动启用 "YouTube 嗅探" 脚本

### 问题：无法捕获视频
- 检查扩展是否已启用
- 确认 YouTube 脚本已激活
- 尝试刷新页面重新加载脚本

### 问题：捕获到的资源无法下载
- YouTube 视频 URL 通常有时效性和访问限制
- 建议使用专门的 YouTube 下载工具

## 开发说明

如需修改或扩展功能：

1. **修改正则规则**：编辑 `js/init.js` 中的 `Regex` 数组
2. **更新脚本逻辑**：修改 `catch-script/youtube.js` 文件
3. **调试模式**：将 `YOUTUBE_DEBUG` 设置为 `true` 启用详细日志

## 版本历史

- v1.0: 初始版本，支持基本的 YouTube 视频嗅探
- 支持视频流、音频流、字幕和清单文件检测
- 集成到 Cat Catch 脚本系统中

## 许可证

本功能遵循 Cat Catch 扩展的许可证条款。
