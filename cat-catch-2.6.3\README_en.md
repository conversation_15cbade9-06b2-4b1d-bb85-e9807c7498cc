<p align="center"> [<a href="README.md">中文</a>] | [English]</p>

# 📑Introduction
Cat-Catch is a resource sniffing extension that can help you filter and list the resources of the current page.

# 📖Installation
## 🐴Chrome
https://chrome.google.com/webstore/detail/jfedfbgedapdagkghmgibemcoggfppbb
## 🦄Edge
https://microsoftedge.microsoft.com/addons/detail/oohmdefbjalncfplafanlagojlakmjci
## 🦊Firefox
https://addons.mozilla.org/addon/cat-catch/ 😂Non-China IP required for access
## 📱Edge Android
<img src="https://raw.githubusercontent.com/xifangczy/cat-catch/master/README/edgeqrcode.png" width="20%" />

💔Cat-Catch is open source, anyone can download, modify, and list it in the app store. There are already quite a few fake Cat-Catch extensions listed with added ad codes, please pay attention to your data security. All installation URLs are subject to github and user documentation.

# 📒Documentation
https://cat-catch.bmmmd.com/

# 🌏Translations
[![gitlocalized ](https://gitlocalize.com/repo/9392/whole_project/badge.svg)](https://gitlocalize.com/repo/9392?utm_source=badge)

# 📘 Installation Methods
## App Store Installation
Install directly from the official extension store using the link provided.
## Source Code Installation
1. Git clone the repository.
2. Open the extensions management page and enable "Developer Mode."
3. Click "Load unpacked" and select the extension folder.
## CRX Installation
1. **Right-click** and save the CRX file from [Releases](https://github.com/xifangczy/cat-catch/releases).
2. Open the extensions management page and enable "Developer Mode."
3. Drag the CRX file into the extensions page.

# 📚Compatibility
After version 1.0.17, Chromium kernel version 93 or above is required.
Use version 1.0.16 if below 93.
For full functionality, use version 104 or above.

# 🔍Screenshot
![popup Screenshot](https://raw.githubusercontent.com/xifangczy/cat-catch/master/README/popup.png)
![m3u8 parser Screenshot](https://raw.githubusercontent.com/xifangczy/cat-catch/master/README/m3u8.png)

# 🤚🏻Disclaimer
This extension is intended for downloading videos that you own or have authorized access to. It is prohibited to use this Tool for downloading copyrighted content without permission. Users are solely responsible for their actions, and the developer is not liable for any user behavior. This Tool is provided "as-is," and the developer assumes no direct or indirect liability.

# 🔒Privacy Policy
The extension collects and processes all information locally without sending it to remote servers and does not include any trackers.

# 💖Acknowledgements
- [hls.js](https://github.com/video-dev/hls.js)
- [jQuery](https://github.com/jquery/jquery)
- [mux.js](https://github.com/videojs/mux.js)
- [js-base64](https://github.com/dankogai/js-base64)
- [jquery.json-viewer](https://github.com/abodelot/jquery.json-viewer)
- [Momo707577045](https://github.com/Momo707577045)
- [mpd-parser](https://github.com/videojs/mpd-parser)
- [StreamSaver.js](https://github.com/jimmywarting/StreamSaver.js)

# 📜License
GPL-3.0 license

Version 1.0 uses the MIT license.

Version 2.0 has changed to the GPL v3 license.

In order for the resource sniffing extension to develop well, it is hoped that extensions using the Cat-Catch source code will continue to be open source.
