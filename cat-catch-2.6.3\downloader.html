<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <title>titleDownload</title>
    <link rel="stylesheet" type="text/css" href="css/public.css" media="all" />
    <link rel="stylesheet" type="text/css" href="css/options.css" media="all" />
    <script src="lib/jquery.min.js"></script>
    <script src="lib/StreamSaver.js"></script>
    <script src="js/init.js"></script>
    <script src="js/firefox.js"></script>
    <script src="js/function.js"></script>
</head>

<body>
    <div class="wrapper1024 hide" id="getURL">
        <section>
            <h1 class="optionsTitle" data-i18n="titleDownload"></h1>
            <div class="optionBox">
                <input type="text" id="url" placeholder="URL" class="fullInput" />
                <input type="text" id="referer" placeholder="referer" class="fullInput" />
                <button id="getURL_btn" type="button" data-i18n="download"></button>
                <label class="textColor"><input type="checkbox" id="downStream"><span
                        data-i18n-outer="downloadWhileSaving"></span></label>
            </div>
        </section>
    </div>

    <div class="wrapper1024 newDownload">
        <section id="downfile">
            <h1 class="optionsTitle" data-i18n="titleDownload"></h1>
            <div class="optionBox" id="downBox"></div>
        </section>
        <section>
            <div class="optionBox">
                <button class="openDir button2" data-i18n="openDir"></button>
                <button id="ffmpeg" class="button2 hide" data-i18n="sendFfmpeg"></button>
                <button id="stopDownload" class="button2" data-i18n="stopDownload"></button>
                <button id="test" class="button2 hide">test</button>
                <label class="textColor"><input type="checkbox" id="autoClose"><span
                        data-i18n-outer="autoCloserDownload"></span></label>
            </div>
        </section>
    </div>

    <script src="js/m3u8.downloader.js"></script>
    <script src="js/downloader.js"></script>
    <script src="js/i18n.js"></script>
</body>

</html>