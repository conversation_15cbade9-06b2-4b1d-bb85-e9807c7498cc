{"background": {"service_worker": "js/background.js"}, "action": {"default_icon": "img/icon.png", "default_title": "__MSG_catCatch__", "default_popup": "popup.html"}, "description": "__MSG_description__", "icons": {"64": "img/icon.png", "128": "img/icon128.png"}, "manifest_version": 3, "minimum_chrome_version": "93", "name": "__MSG_catCatch__", "homepage_url": "https://github.com/xifangczy/cat-catch", "options_ui": {"page": "options.html", "open_in_tab": true}, "permissions": ["tabs", "webRequest", "downloads", "storage", "webNavigation", "alarms", "declarativeNetRequest", "scripting", "sidePanel"], "commands": {"_execute_action": {}, "enable": {"description": "__MSG_pause__ / __MSG_enable__"}, "auto_down": {"description": "__MSG_autoDownload__"}, "catch": {"description": "__MSG_cacheCapture__"}, "m3u8": {"description": "__MSG_m3u8Parser__"}, "clear": {"description": "__MSG_clear__"}}, "host_permissions": ["*://*/*", "<all_urls>"], "content_scripts": [{"matches": ["https://*/*", "http://*/*"], "js": ["js/content-script.js"], "run_at": "document_start", "all_frames": true}], "web_accessible_resources": [{"resources": ["catch-script/*"], "matches": ["<all_urls>"]}], "default_locale": "en", "version": "2.6.3", "incognito": "split"}