<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 嗅探功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cat Catch YouTube 嗅探功能测试</h1>
        
        <div class="status info">
            <strong>使用说明：</strong>
            <ol>
                <li>确保已安装并启用 Cat Catch 扩展</li>
                <li>在扩展的弹出窗口中启用 "YouTube 嗅探" 脚本</li>
                <li>点击下面的测试按钮来模拟 YouTube 视频请求</li>
                <li>查看 Cat Catch 扩展是否捕获到视频资源</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>1. 模拟 YouTube 视频播放请求</h3>
            <button onclick="testVideoPlayback()">测试视频播放 URL</button>
            <button onclick="testAudioPlayback()">测试音频播放 URL</button>
            <button onclick="testManifest()">测试 DASH 清单</button>
            <button onclick="testSubtitles()">测试字幕文件</button>
        </div>

        <div class="test-section">
            <h3>2. 模拟 YouTube API 请求</h3>
            <button onclick="testPlayerResponse()">测试播放器响应</button>
            <button onclick="testMultipleFormats()">测试多格式响应</button>
        </div>

        <div class="test-section">
            <h3>3. 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 模拟 YouTube 视频播放请求
        function testVideoPlayback() {
            log('测试视频播放 URL...');
            const testUrl = 'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?expire=1234567890&ei=test&ip=127.0.0.1&id=o-test&itag=22&source=youtube&requiressl=yes&mime=video%2Fmp4';
            
            fetch(testUrl, { mode: 'no-cors' })
                .then(() => log('✓ 视频播放请求已发送'))
                .catch(err => log('✓ 视频播放请求已发送 (预期的 CORS 错误)'));
        }

        // 模拟 YouTube 音频播放请求
        function testAudioPlayback() {
            log('测试音频播放 URL...');
            const testUrl = 'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?expire=1234567890&ei=test&ip=127.0.0.1&id=o-test&itag=140&source=youtube&requiressl=yes&mime=audio%2Fmp4';
            
            fetch(testUrl, { mode: 'no-cors' })
                .then(() => log('✓ 音频播放请求已发送'))
                .catch(err => log('✓ 音频播放请求已发送 (预期的 CORS 错误)'));
        }

        // 模拟 DASH 清单请求
        function testManifest() {
            log('测试 DASH 清单 URL...');
            const testUrl = 'https://www.youtube.com/api/manifest/dash/id/test123/source/youtube';
            
            fetch(testUrl, { mode: 'no-cors' })
                .then(() => log('✓ DASH 清单请求已发送'))
                .catch(err => log('✓ DASH 清单请求已发送 (预期的 CORS 错误)'));
        }

        // 模拟字幕请求
        function testSubtitles() {
            log('测试字幕文件 URL...');
            const testUrl = 'https://www.youtube.com/api/timedtext?v=test123&lang=zh-CN&fmt=srv3';
            
            fetch(testUrl, { mode: 'no-cors' })
                .then(() => log('✓ 字幕文件请求已发送'))
                .catch(err => log('✓ 字幕文件请求已发送 (预期的 CORS 错误)'));
        }

        // 模拟播放器响应
        function testPlayerResponse() {
            log('测试播放器响应...');
            const testUrl = 'https://www.youtube.com/youtubei/v1/player?key=test123';
            
            // 模拟播放器响应数据
            const mockResponse = {
                streamingData: {
                    adaptiveFormats: [
                        {
                            url: 'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?test=1',
                            mimeType: 'video/mp4; codecs="avc1.640028"',
                            quality: '720p'
                        },
                        {
                            url: 'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?test=2',
                            mimeType: 'audio/mp4; codecs="mp4a.40.2"',
                            quality: 'medium'
                        }
                    ],
                    formats: [
                        {
                            url: 'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?test=3',
                            mimeType: 'video/mp4',
                            quality: '360p'
                        }
                    ]
                }
            };

            // 模拟 XHR 请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', testUrl);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            // 模拟响应
            Object.defineProperty(xhr, 'responseText', {
                value: JSON.stringify(mockResponse),
                writable: false
            });
            Object.defineProperty(xhr, 'status', {
                value: 200,
                writable: false
            });
            Object.defineProperty(xhr, 'readyState', {
                value: 4,
                writable: false
            });

            // 触发事件
            setTimeout(() => {
                xhr.dispatchEvent(new Event('readystatechange'));
                log('✓ 播放器响应已模拟');
            }, 100);
        }

        // 测试多格式响应
        function testMultipleFormats() {
            log('测试多格式响应...');
            
            const urls = [
                'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?itag=22&mime=video%2Fmp4',
                'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?itag=140&mime=audio%2Fmp4',
                'https://r1---sn-gwpa-jj0e.googlevideo.com/videoplayback?itag=18&mime=video%2Fmp4'
            ];

            urls.forEach((url, index) => {
                setTimeout(() => {
                    fetch(url, { mode: 'no-cors' })
                        .then(() => log(`✓ 多格式请求 ${index + 1} 已发送`))
                        .catch(err => log(`✓ 多格式请求 ${index + 1} 已发送 (预期的 CORS 错误)`));
                }, index * 500);
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('YouTube 嗅探功能测试页面已加载');
            log('请确保已启用 Cat Catch 扩展的 YouTube 嗅探脚本');
        });
    </script>
</body>
</html>
