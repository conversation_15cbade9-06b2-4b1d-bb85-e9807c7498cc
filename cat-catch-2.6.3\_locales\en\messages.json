{"catCatch": {"message": "cat-catch"}, "description": {"message": "Web media sniffing tool"}, "confirm": {"message": "Confirm"}, "currentPage": {"message": "Current Page"}, "otherPage": {"message": "Other Page"}, "otherFeatures": {"message": "Other Features"}, "mediaControl": {"message": "Media Control"}, "loadingData": {"message": "Loading Data..."}, "selectWebpage": {"message": "Webpage:"}, "selectMedia": {"message": "Media:"}, "noMediaDetected": {"message": "No Media Detected on Webpage"}, "noControllableMediaDetected": {"message": "No Controllable Media Detected"}, "multiplier": {"message": "Multiplier:"}, "speedPlayback": {"message": "Speed Playback"}, "play": {"message": "Play"}, "normalPlay": {"message": "Normal Playback"}, "pictureInPicture": {"message": "Picture in Picture"}, "fullscreen": {"message": "Fullscreen"}, "screenshot": {"message": "Screenshot"}, "loop": {"message": "Loop"}, "mute": {"message": "Mute"}, "volume": {"message": "Volume"}, "functionEntry": {"message": "Function Entry"}, "downloader": {"message": "Downloader"}, "parser": {"message": "<PERSON><PERSON><PERSON>"}, "m3u8Parser": {"message": "M3U8 Parser"}, "mpdParser": {"message": "MPD Parser"}, "jsonFormatter": {"message": "JSON Formatter"}, "expandAll": {"message": "Expand All"}, "expandPlayable": {"message": "Expand Playable"}, "expandSelected": {"message": "Expand Selected"}, "collapseAll": {"message": "Collapse All"}, "videoRecording": {"message": "Video Recording"}, "closeRecording": {"message": "Close Recording"}, "recordWebRTC": {"message": "Record WebRTC"}, "screenCapture": {"message": "Screen Capture"}, "simulateMobile": {"message": "Simulate Mobile"}, "autoDownload": {"message": "Auto Download"}, "onlineMerge": {"message": "<PERSON><PERSON>"}, "download": {"message": "Download"}, "copy": {"message": "Copy"}, "selectAll": {"message": "Select All"}, "invertSelection": {"message": "Toggle"}, "filter": {"message": "Filter"}, "clear": {"message": "Clear"}, "deepSearch": {"message": "Search"}, "closeSearch": {"message": "Close Search"}, "cacheCapture": {"message": "Capture"}, "closeCapture": {"message": "Close Capture"}, "moreFeatures": {"message": "More"}, "pause": {"message": "Pause"}, "settings": {"message": "Settings"}, "closeSimulation": {"message": "Close Simulation"}, "closeDownload": {"message": "Close Download"}, "enable": {"message": "Enable"}, "disable": {"message": "Disable"}, "noData": {"message": "No Fish"}, "regularFilterPlaceholder": {"message": "Regular expression filter, match resource URL, press Enter to confirm"}, "option": {"message": "Option"}, "titleOption": {"message": "cat-catch Option"}, "titleDownload": {"message": "cat-catch Download"}, "titleM3U8": {"message": "cat-catch m3u8 Parser"}, "titleJson": {"message": "cat-catch json formatter"}, "titledash": {"message": "cat-catch <PERSON>"}, "suffix": {"message": "Suffix"}, "suffixTip": {"message": "Fill in the suffix that does not contain '.', if no size filtering is needed, fill in 0."}, "extensionName": {"message": "Extension Name"}, "filterSize": {"message": "<PERSON><PERSON>"}, "delete": {"message": "Delete"}, "addSuffix": {"message": "Add Suffix"}, "extension": {"message": "Extension"}, "disableAll": {"message": "Disable All"}, "enableAll": {"message": "Enable All"}, "type": {"message": "Type"}, "addType": {"message": "Add Type"}, "typeTip": {"message": "Enter the correct content-type, if no size filtering is needed, fill in 0."}, "addTypeError": {"message": "The format of the capture type is incorrect, please check"}, "regexMatch": {"message": "Regex Match"}, "blockResource": {"message": "Block Resource"}, "alert": {"message": "<PERSON><PERSON>"}, "regexExpression": {"message": "Regex Expression"}, "addRegex": {"message": "Add Regex"}, "regexTest": {"message": "Regex test"}, "regex": {"message": "regex"}, "flag": {"message": "Flag"}, "result": {"message": "Result"}, "match": {"message": "Match"}, "noMatch": {"message": "No Match"}, "blockResourceTip": {"message": "Block the resources you do not want to appear"}, "flagTip": {"message": "i: ignore case, g: global search. It can also be left blank"}, "regexSuffixTip": {"message": "Assign a suffix to the obtained URL. It can be left blank, and the suffix will be automatically truncated (many files do not have a suffix)"}, "regexTip": {"message": "Regular expressions consume a lot of resources, use them carefully if not necessary"}, "copyTip": {"message": "For the convenience of using third-party applications, customize the content written to the clipboard by the copy button"}, "replaceKeywordList": {"message": "Replace the Keyword List"}, "otherFiles": {"message": "Other Files"}, "resetCopySettings": {"message": "Reset Copy Settings"}, "autoSetRefererCookieParams": {"message": "Automatically Set Referer and <PERSON>ie Parameters"}, "secretKey": {"message": "Secret Key"}, "address": {"message": "Address"}, "documentation": {"message": "Documentation"}, "aria2Tip": {"message": "An excellent download tool, see how to use"}, "m3u8DLTips": {"message": "An excellent third-party m3u8 and mpd download tool, see how to use"}, "invoke": {"message": "Invoke"}, "parameter": {"message": "Parameter"}, "parameterSetting": {"message": "Parameter Setting"}, "test": {"message": "Test"}, "replaceTags": {"message": "Replace Tags"}, "customSaveFileName": {"message": "Custom Save File Name"}, "userAgentTip": {"message": "Default to the current browser's User Agent"}, "alwaysDisableCatCatcher": {"message": "Always Disable Cat-Catch Downloader"}, "autoClosePageAfterDownload": {"message": "Automatically Close Page After Download"}, "openDownloaderPageInBackground": {"message": "Open Downloader Page in Background"}, "downloaderTip": {"message": "If the resource download fails, automatically enable the downloader to try again."}, "autoDownM3u8Tip": {"message": "Click the download button and use the m3u8 parser to start merging and downloading immediately"}, "otherSettings": {"message": "Other Settings"}, "resetOtherSettings": {"message": "Reset Other Settings"}, "previewMode": {"message": "Use the local player's call protocol to open video previews"}, "previewModePlaceholder": {"message": "Leave it blank to disable it. The default is to use the popup page to preview the video"}, "preview": {"message": "Preview"}, "customFilenameOption": {"message": "Use a custom filename to save the file (the default is the webpage title)"}, "saveAsOption": {"message": "Choose the save directory after downloading"}, "iconOption": {"message": "Display the website icon"}, "clearOption": {"message": "Refresh, navigate to a new page, and clear the data captured by the current tab"}, "doNotClear": {"message": "Do Not Clear"}, "normalClear": {"message": "Normal Clear"}, "moreFrequent": {"message": "More Frequent"}, "excludeDuplicateResources": {"message": "Exclude duplicate resources (too many resources will consume a lot of CPU)"}, "customCSS": {"message": "Custom CSS"}, "operation": {"message": "Operation"}, "exportSettings": {"message": "Export Settings"}, "importConfiguration": {"message": "Import Configuration"}, "clearCapturedData": {"message": "Clear Captured Data"}, "resetSettings": {"message": "Reset Settings"}, "resetAllSettings": {"message": "Reset All Settings"}, "restartExtension": {"message": "Restart Extension"}, "about": {"message": "About"}, "confirmReset": {"message": "Are you sure you want to reset?"}, "invokeProtocolTemplate": {"message": "Invoke Protocol Template"}, "customVLCProtocol": {"message": "Custom VLC Protocol"}, "systemShare": {"message": "System Share"}, "default": {"message": "<PERSON><PERSON><PERSON>"}, "goBack": {"message": "Go Back"}, "openDir": {"message": "Open Directory"}, "downloadDir": {"message": "Download Directory"}, "sendFfmpeg": {"message": "Send to Online ffmpeg"}, "autoCloserDownload": {"message": "Automatically Close Page After Download"}, "openInBgDownload": {"message": "Open Downloader Page in Background"}, "m3u8Placeholder": {"message": "Enter m3u8 content or ts fragment list."}, "m3u8Url": {"message": "m3u8 URL"}, "nextLevel": {"message": "Next Level"}, "nextLevelTip": {"message": "This M3U8 file nests multiple M3U8 files."}, "multipleAudios": {"message": "Multiple Audios"}, "multipleAudiosTip": {"message": "This M3U8 file nests multiple audios"}, "multipleSubtitles": {"message": "Multiple Subtitles"}, "multipleSubtitlesTip": {"message": "This M3U8 file nests multiple subtitles."}, "possibleKey": {"message": "Found possible keys"}, "loading": {"message": "Loading..."}, "waitDownload": {"message": "Waiting for download..."}, "downloadSegmentList": {"message": "Download list"}, "originalM3u8": {"message": "Original M3U8"}, "localM3u8": {"message": "Local M3U8"}, "segmentList": {"message": "Segment"}, "downloadProgress": {"message": "Download Progress"}, "getParameters": {"message": "GET Parameters"}, "restoreGetParameters": {"message": "Restore GET Parameters"}, "requestHeaders": {"message": "Request Headers"}, "setRequestHeaders": {"message": "Set request headers."}, "invokeM3u8DL": {"message": "Invoke M3U8DL"}, "copyCommand": {"message": "Copy Command"}, "previewCommand": {"message": "Preview Command"}, "addSettingParameters": {"message": "Add Setting Parameters"}, "customKeyPlaceholder": {"message": "Customize the key in hexadecimal or base64, or the key address"}, "uploadKey": {"message": "Upload key"}, "downloadThreads": {"message": "Threads"}, "ffmpegTranscoding": {"message": "FFmpeg transcod"}, "mp4Format": {"message": "MP4"}, "downloadWhileSaving": {"message": "Stream download"}, "audioOnly": {"message": "Audio Only"}, "saveAs": {"message": "Save As"}, "skipDecryption": {"message": "<PERSON><PERSON>"}, "newDownloader": {"message": "New Downloader"}, "downloadRange": {"message": "Download Range"}, "recordLive": {"message": "Record"}, "mergeDownloads": {"message": "Merge Downloads"}, "redownloadFailedItems": {"message": "Redownload Failed Items"}, "downloadExistingData": {"message": "Download Existing Data"}, "stopDownload": {"message": "Stop Download"}, "start": {"message": "Start"}, "end": {"message": "End"}, "resolution": {"message": "Resolution"}, "duration": {"message": "Duration"}, "bitrate": {"message": "Bitrate"}, "ADTSerror": {"message": "Cannot find the ADTS header. It may be an AES-128-ECB encrypted resource, which is not currently supported for decryption. Please use third-party merging software."}, "m3u8Error": {"message": "There are errors in parsing or playing the M3U8 file, check the console for detailed error information"}, "noAudio": {"message": "No Audio"}, "noVideo": {"message": "No Video"}, "hevcTip": {"message": "HEVC/H.265 encoded fragment files are only supported for online ffmpeg transcoding"}, "hevcPreviewTip": {"message": "HEVC/H.265 encoded fragment files are not supported for preview."}, "m3u8Info": {"message": "A total of $num$ file, with a total duration of $time$.", "placeholders": {"num": {"content": "$1"}, "time": {"content": "$2"}}}, "encryptedHLS": {"message": "Encrypted HLS"}, "encryptedSAMPLE": {"message": "Resources encrypted with SAMPLE-AES-CTR cannot be handled at the moment."}, "liveHLS": {"message": "Live HLS"}, "keyAddress": {"message": "Key Address"}, "key": {"message": "Key"}, "encryptionAlgorithm": {"message": "Method"}, "keyDownloadFailed": {"message": "Key Download Failed"}, "savePrompt": {"message": "Saved to disk, please check the downloaded content in the browser."}, "close": {"message": "Close"}, "blobM3u8DLError": {"message": "Blob URLs cannot invoke M3U8DL for download"}, "M3U8DLparameterLong": {"message": "The M3U8DL parameter is too long."}, "runningCannotChangeSettings": {"message": "Running, Cannot Change Settings"}, "streamSaverTip": {"message": "The function of 'download while saving' does not support ffmpeg online format conversion, does not support re-downloading erroneous slices, and does not support 'save as'."}, "stopRecording": {"message": "Stop Recording"}, "waitingForLiveData": {"message": "Waiting for Live Data"}, "sNumError": {"message": "Serial Number Error"}, "startGTend": {"message": "Start Number Cannot Be Greater Than End Number"}, "sNumMax": {"message": "Serial Number Cannot Exceed $num$", "placeholders": {"num": {"content": "$1"}}}, "incorrectKey": {"message": "Incorrect Key"}, "addParameters": {"message": "Add Parameters"}, "decryptionError": {"message": "Decryption Error"}, "downloadFailed": {"message": "Download Failed"}, "retryDownload": {"message": "Retry Download"}, "recordingDuration": {"message": "Recording Duration"}, "downloaded": {"message": "Downloaded"}, "downloadedVideoLength": {"message": "Downloaded Video Length"}, "downloadComplete": {"message": "Download Complete"}, "retryingDownload": {"message": "Retrying Download"}, "merging": {"message": "Merging"}, "fileTooLarge": {"message": "File Too Large, File larger than $size$", "placeholders": {"size": {"content": "$1"}}}, "fileTooLargeStream": {"message": "File larger than $size$, enable stream download?", "placeholders": {"size": {"content": "$1"}}}, "formatConversionError": {"message": "Format Conversion Error"}, "streamOnbeforeunload": {"message": "Streaming is in progress, the download will stop after closing"}, "fileLoading": {"message": "File Loading"}, "expandAllNodes": {"message": "Expand all JSON nodes"}, "collapseAllNodes": {"message": "Collapse all JSON nodes"}, "fileRetrievalFailed": {"message": "File Retrieval Failed"}, "selectVideo": {"message": "Select Video"}, "extractSlices": {"message": "Extract Slices"}, "convertToM3U8": {"message": "Convert to M3U8 Parsing"}, "selectAudio": {"message": "Select Audio"}, "audio": {"message": "Audio"}, "video": {"message": "Video"}, "DRMerror": {"message": "The media has DRM protection, please use third-party tools for download"}, "regexTitle": {"message": "Regular expression match or from deep search"}, "downloadWithRequestHeader": {"message": "Download with request header parameters."}, "m3u8Playlist": {"message": "M3U8 Playlist"}, "copiedToClipboard": {"message": "Copied to Clipboard"}, "hasSent": {"message": "<PERSON><PERSON>"}, "sendFailed": {"message": "Send Failed"}, "confirmDownload": {"message": "$num$ files in total, confirm download?", "placeholders": {"num": {"content": "$1"}}}, "confirmLoading": {"message": "There are $num$ resources in total, do you want to cancel the loading?", "placeholders": {"num": {"content": "$1"}}}, "waitingForMedia": {"message": "Waiting to receive media files... Please do not close this page."}, "exit": {"message": "Exit"}, "unknownSize": {"message": "Unknown size"}, "saving": {"message": "Saving"}, "saveFailed": {"message": "Save failed"}, "badgeNumber": {"message": "Show icon badge prompt"}, "viewSlices": {"message": "View all slices and download progress"}, "send2local": {"message": "Data transmission"}, "popup": {"message": "Popup"}, "defaultPopup": {"message": "Default <PERSON>up <PERSON>"}, "invokeApp": {"message": "Invoke application"}, "onlineServiceAddress": {"message": "Online Service Address"}, "withinChina": {"message": "Within China"}, "dataFetchFailed": {"message": "Data fetch failed"}, "confirmParameters": {"message": "Confirm Parameters"}, "searchingForRealKey": {"message": "Searching for real key"}, "verifying": {"message": "Verifying"}, "realKeyNotFound": {"message": "Real key not found"}, "blockUrl": {"message": "Block URL"}, "addUrl": {"message": "Add URL"}, "wildcards": {"message": "wildcards"}, "blockUrlTips": {"message": "Support wildcards * and ?"}, "setWhiteList": {"message": "Set to whitelist"}, "autoSend": {"message": "Automatic data transmission"}, "manualSend": {"message": "Manual data transmission"}, "requestMethod": {"message": "Request Method"}, "requestBody": {"message": "Request Body"}, "sort": {"message": "Sort"}, "asc": {"message": "Ascending"}, "desc": {"message": "Descending"}, "getTime": {"message": "Retrieval Time"}, "fileSize": {"message": "File Size"}, "title": {"message": "Title"}, "noKeyIsRequired": {"message": "No key is required"}, "estimateSize": {"message": "Estimated size"}, "retryCount": {"message": "Retry count"}, "useSidePanel": {"message": "Use side panel"}}