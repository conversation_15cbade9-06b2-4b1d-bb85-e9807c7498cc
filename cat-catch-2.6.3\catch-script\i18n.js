window.CatCatchI18n = {
    languages: ["en", "zh"],
    downloadCapturedData: {
        en: "Download the captured data",
        zh: "下载已捕获的数据"
    },
    deleteCapturedData: {
        en: "Delete the captured data",
        zh: "删除已捕获数据"
    },
    capturedBeginning: {
        en: "Capture from the beginning",
        zh: "从头捕获"
    },
    alwaysCapturedBeginning: {
        en: "Always Capture from the beginning",
        zh: "始终从头捕获"
    },
    hide: {
        en: "Hide",
        zh: "隐藏"
    },
    close: {
        en: "Close",
        zh: "关闭"
    },
    save: {
        en: "Save",
        zh: "保存"
    },
    automaticDownload: {
        en: "Automatic download",
        zh: "完成捕获自动下载"
    },
    ffmpeg: {
        en: "using ffmpeg",
        zh: "使用ffmpeg"
    },
    fileName: {
        en: "File name",
        zh: "文件名"
    },
    selector: {
        en: "Selector",
        zh: "表达式"
    },
    regular: {
        en: "Regular",
        zh: "正则"
    },
    notSet: {
        en: "Not set",
        zh: "未设置"
    },
    usingSelector: {
        en: "selector",
        zh: "表达式提取"
    },
    usingRegular: {
        en: "regular",
        zh: "正则提取"
    },
    customize: {
        en: "Customize",
        zh: "自定义"
    },
    cleanHeader: {
        en: "Clean up redundant header data",
        zh: "清理多余头部数据"
    },
    clearCache: {
        en: "Clear cache",
        zh: "清理缓存"
    },
    cleanupCompleted: {
        en: "Cleanup completed",
        zh: "清理完成"
    },
    downloadConfirmation: {
        en: "Downloading in advance may cause data confusion. Confirm?",
        zh: "提前下载可能会造成数据混乱.确认？"
    },
    fileNameError: {
        en: "Unable to fetch or the content is empty!",
        zh: "无法获取或内容为空!"
    },
    noData: {
        en: "No data",
        zh: "没抓到有效数据!"
    },
    waiting: {
        en: "Waiting for video to play",
        zh: "等待视频播放"
    },
    capturingData: {
        en: "Capturing data",
        zh: "捕获数据中"
    },
    captureCompleted: {
        en: "Capture completed",
        zh: "捕获完成"
    },
    downloadCompleted: {
        en: "Download completed",
        zh: "下载完毕"
    },
    selectVideo: {
        en: "Select Video",
        zh: "选择视频"
    },
    recordEncoding: {
        en: "Record Encoding",
        zh: "录制编码"
    },
    readVideo: {
        en: "Read Video",
        zh: "读取视频"
    },
    startRecording: {
        en: "Start Recording",
        zh: "开始录制"
    },
    stopRecording: {
        en: "Stop Recording",
        zh: "停止录制"
    },
    noVideoDetected: {
        en: "No video detected, Please read again",
        zh: "没有检测到视频, 请重新读取"
    },
    recording: {
        en: "Recording",
        zh: "视频录制中"
    },
    recordingNotSupported: {
        en: "recording Not Supported",
        zh: "不支持录制"
    },
    formatNotSupported: {
        en: "Format not supported",
        zh: "不支持此格式"
    },
    clickToStartRecording: {
        en: "Click to start recording",
        zh: "请点击开始录制"
    },
    sentToFfmpeg: {
        en: "Sent to ffmpeg",
        zh: "发送到ffmpeg"
    },
    recordingFailed: {
        en: "Recording failed",
        zh: "录制失败"
    },
    scriptNotSupported: {
        en: "This script is not supported",
        zh: "当前网页不支持此脚本"
    },
    dragWindow: {
        en: "Drag window",
        zh: "拖动窗口"
    },
    autoToBuffered: {
        en: "Automatically jump to buffer",
        zh: "自动跳转到缓冲尾"
    },
    save1hour: {
        en: "Save once every hour",
        zh: "1小时保存一次"
    },
    recordingChangeEncoding: {
        en: "Cannot change encoding during recording",
        zh: "录制中不能更改编码"
    },
    streamEmpty: {
        en: "Media stream is empty",
        zh: "媒体流为空"
    },
    notStream: {
        en: "Not a media stream object",
        zh: "非媒体流对象"
    },
    notStream: {
        en: "Not a media stream object",
        zh: "非媒体流对象"
    },
    streamAdded: {
        en: "Stream added",
        zh: "流已添加"
    },
    videoAndAudio: {
        en: "Includes both audio and video streams",
        zh: "已包含音频和视频流"
    },
    audioBits: {
        en: "Audio bit",
        zh: "音频码率"
    },
    videoBits: {
        en: "Video bits",
        zh: "视频码率"
    },
    frameRate: {
        en: "frame Rate",
        zh: "帧率"
    },
    noHeader: {
        en: "No header data detected, please process with local tools",
        zh: "没有检测到视频头部数据, 请使用本地工具处理"
    },
    headData: {
        en: "Multiple header data found in media file, Clear it?",
        zh: "检测到多余头部数据, 是否清除?"
    },
    clearCacheConfirmation: {
        en: "Are you sure you want to clear the cache?",
        zh: "确定要清除缓存吗?"
    },
    closeConfirmation: {
        en: "Are you sure you want to close?",
        zh: "确定要关闭吗?"
    }
};